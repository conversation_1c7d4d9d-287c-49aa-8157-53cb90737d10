# 退款功能部署检查清单

## 📋 部署前检查

### 1. 数据库准备
- [ ] 备份现有数据库
- [ ] 执行数据库迁移脚本 `database_migrations/add_refund_table.sql`
- [ ] 验证 `refund_record` 表创建成功
- [ ] 检查外键约束和索引

### 2. 代码文件检查
- [ ] `models.py` - 新增 RefundRecord 模型
- [ ] `utils.py` - 新增 process_refund 函数
- [ ] `app.py` - 新增退款相关API路由
- [ ] `templates/history.html` - 新增退款界面和功能

### 3. 依赖检查
- [ ] 确认所有Python依赖包已安装
- [ ] 检查数据库连接配置
- [ ] 验证Flask应用配置

## 🚀 部署步骤

### 步骤1：数据库更新
```bash
# 1. 备份数据库
mysqldump -u username -p database_name > backup_$(date +%Y%m%d_%H%M%S).sql

# 2. 执行迁移脚本
mysql -u username -p database_name < database_migrations/add_refund_table.sql

# 3. 验证表结构
mysql -u username -p database_name -e "DESCRIBE refund_record;"
```

### 步骤2：代码部署
```bash
# 1. 停止应用服务
sudo systemctl stop your-app-service

# 2. 更新代码文件
# 确保以下文件已更新：
# - models.py
# - utils.py  
# - app.py
# - templates/history.html

# 3. 重启应用服务
sudo systemctl start your-app-service
sudo systemctl status your-app-service
```

### 步骤3：功能验证
- [ ] 访问历史订单页面
- [ ] 查看订单详情，确认退款按钮显示
- [ ] 测试退款模态框打开
- [ ] 测试表单验证
- [ ] 测试退款处理流程

## 🧪 测试用例

### 测试用例1：全额退款（余额退回）
1. 选择一个已完成的订单
2. 点击退款按钮
3. 选择全额退款
4. 选择"余额退回"
5. 填写退款原因
6. 确认退款
7. 验证：
   - [ ] 退款记录创建成功
   - [ ] 订单状态更新为"已退赔"
   - [ ] 客户余额增加
   - [ ] 页面显示成功信息

### 测试用例2：部分退款（现金退款）
1. 选择一个已完成的订单
2. 点击退款按钮
3. 输入部分退款金额
4. 选择"现金退款"
5. 填写退款原因
6. 确认退款
7. 验证：
   - [ ] 退款记录创建成功
   - [ ] 订单状态保持不变
   - [ ] 客户余额不变
   - [ ] 页面显示成功信息

### 测试用例3：权限验证
1. 使用普通员工账号登录
2. 尝试退款其他员工的订单
3. 验证：
   - [ ] 显示权限错误信息
   - [ ] 退款操作被拒绝

### 测试用例4：边界值测试
1. 测试退款金额为0
2. 测试退款金额超过订单金额
3. 测试空的退款原因
4. 验证：
   - [ ] 显示相应的错误信息
   - [ ] 退款操作被拒绝

### 测试用例5：已退赔订单
1. 选择已退赔状态的订单
2. 验证：
   - [ ] 退款按钮不显示
   - [ ] 无法进行二次退款

## 🔍 监控要点

### 1. 应用日志
- [ ] 检查应用启动日志
- [ ] 监控退款操作日志
- [ ] 关注错误日志

### 2. 数据库监控
- [ ] 监控退款记录表数据
- [ ] 检查订单状态更新
- [ ] 验证客户余额变化

### 3. 性能监控
- [ ] 监控退款API响应时间
- [ ] 检查数据库查询性能
- [ ] 观察系统资源使用

## ⚠️ 回滚计划

如果部署出现问题，按以下步骤回滚：

### 1. 代码回滚
```bash
# 1. 停止应用服务
sudo systemctl stop your-app-service

# 2. 恢复原始代码文件
git checkout HEAD~1 -- models.py utils.py app.py templates/history.html

# 3. 重启应用服务
sudo systemctl start your-app-service
```

### 2. 数据库回滚
```bash
# 1. 删除退款记录表（如果需要）
mysql -u username -p database_name -e "DROP TABLE IF EXISTS refund_record;"

# 2. 恢复数据库备份（如果需要）
mysql -u username -p database_name < backup_file.sql
```

## 📞 联系信息

如果部署过程中遇到问题，请联系：
- 技术负责人：[联系方式]
- 系统管理员：[联系方式]

## 📝 部署记录

### 部署信息
- 部署日期：_____________
- 部署人员：_____________
- 版本号：_______________
- 备份文件：_____________

### 验证结果
- [ ] 数据库迁移成功
- [ ] 应用启动正常
- [ ] 功能测试通过
- [ ] 性能测试通过

### 问题记录
如有问题，请记录：
1. 问题描述：
2. 解决方案：
3. 影响范围：

### 签字确认
- 开发人员：_____________ 日期：_______
- 测试人员：_____________ 日期：_______
- 运维人员：_____________ 日期：_______
- 项目负责人：___________ 日期：_______

---

**注意事项：**
1. 请在生产环境部署前，先在测试环境完整验证
2. 部署过程中如遇问题，立即停止并联系技术团队
3. 确保在业务低峰期进行部署
4. 部署完成后持续监控系统运行状态
