-- 性能优化索引创建脚本
-- 执行时间：请在性能优化部署时执行此脚本
-- 注意：在生产环境执行前请先备份数据库

-- =====================================================
-- 1. 检查当前索引状况
-- =====================================================
SELECT '=== 当前索引状况检查 ===' AS info;

-- 检查Customer表索引
SELECT 
    'Customer表当前索引' AS table_info,
    INDEX_NAME,
    COLUMN_NAME,
    CARDINALITY,
    INDEX_TYPE
FROM information_schema.STATISTICS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'customer'
ORDER BY INDEX_NAME;

-- 检查Order表索引  
SELECT 
    'Order表当前索引' AS table_info,
    INDEX_NAME,
    COLUMN_NAME,
    CARDINALITY,
    INDEX_TYPE
FROM information_schema.STATISTICS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'order'
ORDER BY INDEX_NAME;

-- 检查MallCustomer表索引
SELECT 
    'MallCustomer表当前索引' AS table_info,
    INDEX_NAME,
    COLUMN_NAME,
    CARDINALITY,
    INDEX_TYPE
FROM information_schema.STATISTICS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'mall_customer'
ORDER BY INDEX_NAME;

-- =====================================================
-- 2. 创建性能优化索引
-- =====================================================
SELECT '=== 开始创建性能优化索引 ===' AS info;

-- Customer表索引优化
SELECT '正在为Customer表添加索引...' AS status;

-- 会员管理页面排序优化
ALTER TABLE customer ADD INDEX idx_updated_at (updated_at);

-- 会员搜索优化
ALTER TABLE customer ADD INDEX idx_name (name);

-- 复合索引：电话+姓名搜索优化
ALTER TABLE customer ADD INDEX idx_phone_name (phone, name);

-- 会员活跃状态查询优化
ALTER TABLE customer ADD INDEX idx_updated_at_desc (updated_at DESC);

SELECT 'Customer表索引创建完成' AS status;

-- Order表索引优化
SELECT '正在为Order表添加索引...' AS status;

-- 历史订单日期查询优化
ALTER TABLE `order` ADD INDEX idx_created_at (created_at);

-- 营业员筛选优化
ALTER TABLE `order` ADD INDEX idx_operator (operator);

-- 订单状态筛选优化
ALTER TABLE `order` ADD INDEX idx_status (status);

-- 支付方式筛选优化
ALTER TABLE `order` ADD INDEX idx_payment_method (payment_method);

-- 订单号查询优化
ALTER TABLE `order` ADD INDEX idx_order_number (order_number);

-- 复合索引：客户订单历史查询优化
ALTER TABLE `order` ADD INDEX idx_customer_created (customer_id, created_at DESC);

-- 复合索引：营业员订单查询优化
ALTER TABLE `order` ADD INDEX idx_operator_created (operator, created_at DESC);

-- 复合索引：商场客户订单查询优化
ALTER TABLE `order` ADD INDEX idx_mall_customer_created (mall_customer_id, created_at DESC);

-- 复合索引：订单状态和日期查询优化
ALTER TABLE `order` ADD INDEX idx_status_created (status, created_at DESC);

SELECT 'Order表索引创建完成' AS status;

-- MallCustomer表索引优化
SELECT '正在为MallCustomer表添加索引...' AS status;

-- 商场名称搜索优化
ALTER TABLE mall_customer ADD INDEX idx_mall_name (mall_name);

-- 商场状态筛选优化
ALTER TABLE mall_customer ADD INDEX idx_status (status);

-- 区域权限筛选优化
ALTER TABLE mall_customer ADD INDEX idx_area (area);

-- 复合索引：区域+状态筛选优化
ALTER TABLE mall_customer ADD INDEX idx_area_status (area, status);

-- 商场名称模糊搜索优化（全文索引）
-- 注意：MySQL 5.6+支持InnoDB全文索引
ALTER TABLE mall_customer ADD FULLTEXT INDEX ft_mall_name (mall_name);

SELECT 'MallCustomer表索引创建完成' AS status;

-- MallProductDiscount表索引优化
SELECT '正在为MallProductDiscount表添加索引...' AS status;

-- 商场客户折扣查询优化
ALTER TABLE mall_product_discount ADD INDEX idx_mall_customer_id (mall_customer_id);

-- 产品折扣查询优化
ALTER TABLE mall_product_discount ADD INDEX idx_product_name (product_name);

SELECT 'MallProductDiscount表索引创建完成' AS status;

-- MallMonthlyBill表索引优化
SELECT '正在为MallMonthlyBill表添加索引...' AS status;

-- 商场客户账单查询优化
ALTER TABLE mall_monthly_bill ADD INDEX idx_mall_customer_payment (mall_customer_id, payment_status);

-- 账单日期查询优化
ALTER TABLE mall_monthly_bill ADD INDEX idx_bill_year_month (bill_year_month);

-- 账单日期范围查询优化
ALTER TABLE mall_monthly_bill ADD INDEX idx_bill_dates (bill_start_date, bill_end_date);

SELECT 'MallMonthlyBill表索引创建完成' AS status;

-- Staff表索引优化
SELECT '正在为Staff表添加索引...' AS status;

-- 区域权限查询优化
ALTER TABLE staff ADD INDEX idx_area (area);

-- 用户名查询优化（如果不存在）
-- ALTER TABLE staff ADD INDEX idx_username (username); -- 通常已存在唯一索引

-- 角色筛选优化
ALTER TABLE staff ADD INDEX idx_role (role);

-- 活跃状态筛选优化
ALTER TABLE staff ADD INDEX idx_is_active (is_active);

SELECT 'Staff表索引创建完成' AS status;

-- Clothing表索引优化
SELECT '正在为Clothing表添加索引...' AS status;

-- 订单衣物查询优化
ALTER TABLE clothing ADD INDEX idx_order_id (order_id);

-- 客户衣物查询优化
ALTER TABLE clothing ADD INDEX idx_customer_id (customer_id);

-- 衣物状态查询优化
ALTER TABLE clothing ADD INDEX idx_status (status);

SELECT 'Clothing表索引创建完成' AS status;

-- RechargeRecord表索引优化
SELECT '正在为RechargeRecord表添加索引...' AS status;

-- 客户充值记录查询优化
ALTER TABLE recharge_record ADD INDEX idx_customer_created (customer_id, created_at DESC);

-- 充值日期查询优化
ALTER TABLE recharge_record ADD INDEX idx_created_at (created_at);

SELECT 'RechargeRecord表索引创建完成' AS status;

-- =====================================================
-- 3. 验证索引创建结果
-- =====================================================
SELECT '=== 索引创建结果验证 ===' AS info;

-- 统计各表的索引数量
SELECT 
    TABLE_NAME,
    COUNT(DISTINCT INDEX_NAME) as index_count,
    COUNT(*) as total_columns_indexed
FROM information_schema.STATISTICS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME IN ('customer', 'order', 'mall_customer', 'mall_product_discount', 'mall_monthly_bill', 'staff', 'clothing', 'recharge_record')
GROUP BY TABLE_NAME
ORDER BY TABLE_NAME;

-- 显示新创建的索引详情
SELECT 
    TABLE_NAME,
    INDEX_NAME,
    GROUP_CONCAT(COLUMN_NAME ORDER BY SEQ_IN_INDEX) as columns,
    INDEX_TYPE,
    NON_UNIQUE
FROM information_schema.STATISTICS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME IN ('customer', 'order', 'mall_customer', 'mall_product_discount', 'mall_monthly_bill', 'staff', 'clothing', 'recharge_record')
AND INDEX_NAME LIKE 'idx_%'
GROUP BY TABLE_NAME, INDEX_NAME
ORDER BY TABLE_NAME, INDEX_NAME;

SELECT '=== 性能优化索引创建完成 ===' AS info;
SELECT '建议：执行完成后运行 ANALYZE TABLE 命令更新表统计信息' AS suggestion;

-- =====================================================
-- 4. 性能测试查询示例
-- =====================================================
SELECT '=== 性能测试查询示例 ===' AS info;

-- 测试Customer表查询性能
-- EXPLAIN SELECT * FROM customer WHERE updated_at >= DATE_SUB(NOW(), INTERVAL 180 DAY) ORDER BY updated_at DESC LIMIT 10;

-- 测试Order表查询性能  
-- EXPLAIN SELECT * FROM `order` WHERE operator = '张三' AND created_at >= '2024-01-01' ORDER BY created_at DESC;

-- 测试MallCustomer表查询性能
-- EXPLAIN SELECT * FROM mall_customer WHERE area = '北京' AND status = '活跃' ORDER BY mall_name;

-- 测试复合查询性能
-- EXPLAIN SELECT o.*, c.name FROM `order` o JOIN customer c ON o.customer_id = c.id WHERE o.created_at >= '2024-01-01' AND c.phone LIKE '138%';

SELECT '索引优化脚本执行完成！' AS final_status;
