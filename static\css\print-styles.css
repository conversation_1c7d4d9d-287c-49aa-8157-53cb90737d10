/**
 * 打印样式文件
 * 包含小票和水洗唛标签的所有打印相关样式
 */

/* 水洗唛标签样式 */
.label-container {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    justify-content: center;
}

.wash-label {
    width: 101mm;  /* 修改为101mm宽度 */
    height: 16mm;  /* 保持16mm高度 */
    padding: 1px 3px; /* 减小内边距 */
    font-size: 10px; /* 调大字体大小 */
    margin-bottom: 5px;
    position: relative;
    page-break-inside: avoid;
    background-color: white;
    display: flex; /* 使用flex布局 */
    flex-direction: row;
    border: 1px dashed #ccc; /* 添加虚线边框辅助打印定位 */
    box-sizing: border-box; /* 确保边框计入总宽度 */
    overflow: hidden; /* 防止内容溢出 */
    font-family: "Microsoft YaHei", "微软雅黑", Arial, sans-serif; /* 统一字体 */
    font-weight: bold; /* 使用粗体 */
    color: #000; /* 黑色文字 */
}

.label-qr {
    text-align: center;
    margin-right: -4mm; /* 负边距调整为-4mm */
    margin-left: -8mm; /* 负边距调整为-8mm */
    display: flex;
    align-items: center;
    justify-content: center;
    width: 63mm; /* 条码区域宽度调整为63mm */
    padding: 0;
    overflow: visible; /* 允许内容溢出到相邻区域 */
    position: relative; /* 创建新的定位上下文 */
    z-index: 1; /* 确保条码区域在上层 */
    left: -4mm; /* 左偏移调整为-4mm */
}

.label-barcode {
    height: 15mm; /* 条码高度 */
    width: 63mm; /* 条码宽度调整为63mm */
    object-fit: contain; /* 确保完整显示 */
    max-width: 100%; /* 限制最大宽度 */
    margin-left: 0; /* 不需要负边距 */
    vertical-align: middle; /* 确保垂直居中 */
}

.label-info {
    font-size: 10px; /* 调大字体大小 */
    line-height: 1.0; /* 统一行高 */
    display: flex;
    flex-direction: column;
    justify-content: flex-start; /* 顶部对齐 */
    flex: 1;
    padding-left: 0px;
    width: 46mm; /* 信息区域宽度调整为46mm */
    margin-left: -8mm; /* 负边距调整为-8mm */
    position: relative;
    z-index: 2;
    padding-right: 2mm;
    font-weight: bold; /* 使用粗体 */
    color: #000; /* 黑色文字 */
    font-family: "Microsoft YaHei", "微软雅黑", Arial, sans-serif; /* 统一字体 */
}

/* 服务标签和备注信息的组合行 */
.label-service-info-row {
    display: flex;
    align-items: center;
    gap: 3px;
    margin-top: 1px;
    flex-wrap: nowrap;
    overflow: hidden;
    white-space: nowrap;
    width: 100%;
    max-width: 100%;
}

.label-service-badges {
    display: flex;
    gap: 3px; /* 减小间距 */
    margin-top: 0px; /* 移除上边距 */
    flex-wrap: nowrap; /* 不换行 */
    flex-shrink: 0; /* 不收缩 */
    white-space: nowrap;
    overflow: visible;
}

.service-badge {
    display: inline-block;
    padding: 0px 2px;
    background-color: #f5f5f5;
    border: 1px solid #ddd;
    border-radius: 2px;
    font-size: 10px; /* 调大字体大小 */
    margin-bottom: 0px;
    font-weight: bold; /* 使用粗体 */
    white-space: nowrap;
    font-family: "Microsoft YaHei", "微软雅黑", Arial, sans-serif; /* 统一字体 */
    color: #000; /* 黑色文字 */
}

.service-badge.urgent {
    background-color: #ffeeee;
    border-color: #ffcccc;
    color: #000; /* 统一黑色文字 */
    font-weight: bold; /* 使用粗体 */
}

.service-badge.decoration {
    background-color: #eeeeff;
    border-color: #ccccff;
    color: #000; /* 统一黑色文字 */
    font-weight: bold; /* 使用粗体 */
}

.label-first-row {
    display: flex;
    justify-content: space-between;
    font-weight: bold; /* 使用粗体 */
    border-bottom: 1px solid #999;
    padding-bottom: 1px; /* 下边距 */
    margin-bottom: 1px; /* 下边距 */
    font-family: "Microsoft YaHei", "微软雅黑", Arial, sans-serif; /* 统一字体 */
    color: #000; /* 黑色文字 */
}

.label-name-price {
    flex: 1;
    display: flex;
    justify-content: space-between;
    overflow: hidden; /* 防止长文本溢出 */
    white-space: nowrap; /* 防止名称换行 */
}

.label-name-price span:first-child {
    max-width: 60%; /* 限制名称宽度 */
    text-overflow: ellipsis; /* 文本溢出显示省略号 */
    overflow: hidden;
    margin-right: 5px;
}

/* 在同一行显示的备注信息样式 */
.label-inline-info {
    font-size: 10px; /* 调大字体大小 */
    color: #000;
    margin-left: 3px;
    white-space: nowrap;
    font-weight: bold; /* 使用粗体 */
    flex-shrink: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    font-family: "Microsoft YaHei", "微软雅黑", Arial, sans-serif; /* 统一字体 */
}

.label-remarks {
    font-size: 7px;
    color: #000;
    margin-top: 0px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
    line-height: 0.8;
    font-weight: bold;
}

/* 水洗唛瑕疵样式 */
.label-flaw {
    font-size: 7px;
    color: #000;
    margin-top: 0px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
    line-height: 0.8;
    font-weight: bold;
}

/* 水洗唛衣物备注样式 */
.label-item-remarks {
    font-size: 7px;
    color: #000;
    margin-top: 0px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
    line-height: 0.8;
    font-weight: bold;
}

/* 订单号样式 */
.label-order-number {
    font-weight: bold;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}


/* ===== 小票样式 - 统一管理 ===== */

/* 小票容器 */
.receipt {
    font-family: "Microsoft YaHei", "微软雅黑", Arial, sans-serif;
    font-weight: bold;
    width: 80mm;
    margin: 0 auto;
    padding: 10px;
    border: 1px dashed #ccc;
    background-color: white;
    box-sizing: border-box;
    font-size: 21px; /* 从16px增大到21px (约30%增长) */
    line-height: 1.2;
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.1);
    color: #000;
}

/* 小票头部 */
.receipt-header {
    text-align: center;
    margin-bottom: 8px;
}

.receipt-header h4 {
    margin: 3px 0;
    font-size: 23px; /* 从18px增大到23px (约30%增长) */
}

.receipt-header p {
    margin: 5px 0 0;
    font-size: 21px; /* 从16px增大到21px (约30%增长) */
}

/* 小票信息区域 */
.receipt-info {
    margin-bottom: 8px;
    font-size: 18px; /* 从14px增大到18px (约30%增长) */
    line-height: 1.3;
}

.receipt-info p {
    margin: 2px 0;
}

/* 小票表格 */
.receipt-items {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 8px;
    font-size: 17px; /* 从13px增大到17px (约30%增长) */
    table-layout: fixed;
}

/* 列宽定义 */
.receipt-items .col-name {
    width: 33%;
}

.receipt-items .col-quantity {
    width: 14%;
}

.receipt-items .col-service {
    width: 31%;
}

.receipt-items .col-price {
    width: 22%;
}

/* 表头样式 */
.receipt-items th {
    border-bottom: 1px solid #ddd;
    text-align: left;
    padding: 3px 2px;
    font-size: 18px; /* 从14px增大到18px (约30%增长) */
    font-weight: bold;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.receipt-items .th-quantity {
    text-align: center;
}

.receipt-items .th-price {
    text-align: right;
}

/* 表体样式 */
.receipt-items td {
    padding: 2px;
    font-size: 17px; /* 从13px增大到17px (约30%增长) */
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    vertical-align: top;
}

.receipt-items .td-quantity {
    text-align: center;
}

.receipt-items .td-price {
    text-align: right;
}

/* 小票总计 */
.receipt-total {
    border-top: 1px solid #ddd;
    padding-top: 3px;
    text-align: right;
    font-weight: bold;
    font-size: 18px; /* 从14px增大到18px (约30%增长) */
    margin-bottom: 10px;
}

.receipt-total p {
    margin: 2px 0;
}

/* 小票底部 */
.receipt-footer {
    text-align: center;
    margin-top: 5px;
    font-size: 17px; /* 从13px增大到17px (约30%增长) */
    border-top: 1px dashed #ccc;
    padding-top: 5px;
}

.receipt-footer p {
    margin: 1px 0;
}

/* 客户余额信息 */
.receipt-balance-info {
    border-top: 1px dashed #000;
    padding-top: 3px;
    margin-top: 5px;
}

.receipt-balance-info p {
    margin: 1px 0;
    font-size: 10px;
    line-height: 1.2;
}

/* ===== 打印弹窗样式 - 统一管理 ===== */

/* 打印弹窗容器 */
.print-modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
}

.print-modal-content {
    position: relative;
    background-color: #fff;
    margin: 5% auto;
    padding: 20px;
    width: 80%;
    max-width: 600px;
    border-radius: 5px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

.print-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #ddd;
    padding-bottom: 10px;
    margin-bottom: 15px;
}

.print-modal-header h3 {
    margin: 0;
}

.close-print-modal {
    font-size: 24px;
    font-weight: bold;
    background: none;
    border: none;
    cursor: pointer;
}

.print-modal-body {
    max-height: 70vh;
    overflow-y: auto;
    margin-bottom: 15px;
}

.print-modal-footer {
    display: flex;
    justify-content: flex-end;
    border-top: 1px solid #ddd;
    padding-top: 15px;
}

.print-action-btn {
    padding: 8px 16px;
    background-color: #007bff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

.print-action-btn:hover {
    background-color: #0056b3;
}



/* 水洗唛选择器 */
.label-dropdown-container {
    margin: 15px 0;
}

.label-select-container {
    display: flex;
    flex-direction: column;
    margin-bottom: 15px;
}

.label-select-title {
    font-weight: bold;
    margin-bottom: 5px;
}

.label-select {
    width: 100%;
    padding: 8px;
    margin-bottom: 15px;
    border-radius: 4px;
    border: 1px solid #ddd;
}

.label-selection-area {
    margin: 10px 0 20px;
    padding: 10px;
    background-color: #f9f9f9;
    border-radius: 4px;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 10px;
}

#labelSelect {
    padding: 6px;
    border-radius: 4px;
    border: 1px solid #ddd;
    flex-grow: 1;
    margin: 0 10px;
}

.print-all-labels {
    background-color: #007bff;
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 4px;
    cursor: pointer;
    margin-right: 10px;
}

.print-selected-label {
    background-color: #28a745;
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 4px;
    cursor: pointer;
}


/* 打印样式 */
@media print {
    /* 隐藏所有内容 */
    body * {
        visibility: hidden !important;
        display: none !important;
    }

    /* 只显示当前打印内容 */
    #print-container, #print-container * {
        visibility: visible !important;
        display: block !important;
    }

    /* 定位打印内容 */
    #print-container {
        position: absolute !important;
        left: 0 !important;
        top: 0 !important;
        width: 100% !important;
        height: auto !important;
        background-color: white !important;
        z-index: 9999 !important;
        padding: 0 !important;
        margin: 0 !important;
    }

    /* 确保每个标签或小票后分页 */
    .receipt, .wash-label {
        page-break-after: always !important;
        margin: 0 auto !important;
        padding: 10px !important;
        box-sizing: border-box !important;
        width: auto !important;
    }

    /* 小票打印优化 */
    .receipt {
        max-width: 80mm !important;
        width: 80mm !important;
        font-size: 18px !important; /* 从14px增大到18px (约30%增长) */
        border: 1px dashed #000 !important;
        box-shadow: none !important;
        page-break-after: always !important;
    }

    /* 小票表格打印优化 */
    .receipt-items th {
        border-bottom: 1px solid #000 !important;
        font-weight: bold !important;
    }

    /* 打印时显示控制 */
    #print-container, #print-container * {
        visibility: visible !important;
        display: block !important;
        position: initial !important;
    }

    #print-container {
        position: absolute !important;
        left: 0 !important;
        top: 0 !important;
        width: 100% !important;
        height: auto !important;
        padding: 0 !important;
        margin: 0 !important;
        background-color: white !important;
    }

    /* 确保表格内容正确显示 */
    .receipt-items {
        display: table !important;
        visibility: visible !important;
        width: 100% !important;
        border-collapse: collapse !important;
        table-layout: fixed !important;
    }

    /* 打印时的列宽定义 - 关键修复 */
    .receipt-items .col-name {
        width: 33% !important;
    }

    .receipt-items .col-quantity {
        width: 14% !important;
    }

    .receipt-items .col-service {
        width: 31% !important;
    }

    .receipt-items .col-price {
        width: 22% !important;
    }

    /* 确保colgroup在打印时生效 */
    .receipt-items colgroup {
        display: table-column-group !important;
        visibility: visible !important;
    }

    .receipt-items col {
        display: table-column !important;
        visibility: visible !important;
    }

    .receipt-items thead {
        display: table-header-group !important;
        visibility: visible !important;
    }

    .receipt-items tbody {
        display: table-row-group !important;
        visibility: visible !important;
    }

    .receipt-items tr {
        display: table-row !important;
        visibility: visible !important;
    }

    .receipt-items th,
    .receipt-items td {
        display: table-cell !important;
        visibility: visible !important;
        padding: 2px 1px !important;
        border: none !important;
        vertical-align: top !important;
    }

    /* 确保表头在一行显示 */
    .receipt-items thead tr {
        page-break-inside: avoid !important;
        page-break-after: avoid !important;
    }

    .receipt-items th {
        font-weight: bold !important;
        border-bottom: 1px solid #000 !important;
        white-space: nowrap !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
        text-align: left !important;
        font-size: 10px !important;
        padding: 2px 1px !important;
    }

    /* 表头对齐方式 */
    .receipt-items .th-quantity {
        text-align: center !important;
    }

    .receipt-items .th-price {
        text-align: right !important;
    }

    /* 表体对齐方式 */
    .receipt-items .td-quantity {
        text-align: center !important;
    }

    .receipt-items .td-price {
        text-align: right !important;
    }

    /* 隐藏打印时不需要的元素 */
    .close-print-modal, .print-action-btn, .print-modal-header,
    .print-modal-footer, .label-selection-area, .print-modal, .modal {
        display: none !important;
    }

    /* 水洗唛标签样式 */
    .wash-label {
        width: 101mm !important; /* 水洗唛宽度修改为101mm */
        height: 16mm !important; /* 水洗唛高度保持16mm */
        max-width: 101mm !important;
        display: flex !important; /* 恢复flex布局 */
        flex-direction: row !important;
        padding: 1px 3px !important;
        margin: 0 auto 10mm auto !important;
        page-break-after: always !important;
        page-break-inside: avoid !important;
        border: 1px dashed #ccc !important;
        box-sizing: border-box !important;
        overflow: hidden !important;
        background-color: white !important;
        font-size: 10px !important; /* 调大字体大小 */
        font-family: "Microsoft YaHei", "微软雅黑", Arial, sans-serif !important; /* 统一字体 */
        font-weight: bold !important; /* 使用粗体 */
        color: #000 !important; /* 黑色文字 */
    }

    /* 确保条码区域正确显示 */
    .label-qr {
        text-align: center !important;
        margin-right: -4mm !important;
        margin-left: -8mm !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        width: 63mm !important;
        padding: 0 !important;
        overflow: visible !important; /* 允许内容溢出到相邻区域 */
        position: relative !important;
        z-index: 1 !important;
        left: -4mm !important;
    }

    /* 确保条码图像正确显示 */
    .label-barcode {
        height: 15mm !important;
        width: 63mm !important;
        object-fit: contain !important;
        margin-left: 0 !important;
        vertical-align: middle !important;
        max-width: 100% !important;
    }

    /* 确保信息区域正确显示 */
    .label-info {
        flex: 1 !important;
        display: flex !important;
        flex-direction: column !important;
        justify-content: flex-start !important;
        padding-left: 0px !important;
        width: 46mm !important;
        margin-left: -8mm !important;
        font-size: 10px !important; /* 调大字体大小 */
        line-height: 1.0 !important; /* 统一行高 */
        font-weight: bold !important; /* 使用粗体 */
        color: #000 !important;
        position: relative !important;
        z-index: 2 !important;
        padding-right: 2mm !important;
        font-family: "Microsoft YaHei", "微软雅黑", Arial, sans-serif !important; /* 统一字体 */
    }

    /* 服务标签和备注信息的组合行 */
    .label-service-info-row {
        display: flex !important;
        align-items: center !important;
        gap: 3px !important;
        margin-top: 1px !important;
        flex-wrap: nowrap !important;
        overflow: hidden !important;
        white-space: nowrap !important;
        width: 100% !important;
        max-width: 100% !important;
    }

    .label-service-badges {
        display: flex !important;
        gap: 3px !important;
        margin-top: 0px !important;
        flex-wrap: nowrap !important;
        flex-shrink: 0 !important;
        white-space: nowrap !important;
        overflow: visible !important;
    }

    .service-badge {
        display: inline-block !important;
        padding: 0px 2px !important;
        background-color: #f5f5f5 !important;
        border: 1px solid #ddd !important;
        border-radius: 2px !important;
        font-size: 10px !important; /* 调大字体大小 */
        margin-bottom: 0px !important;
        font-weight: bold !important; /* 使用粗体 */
        white-space: nowrap !important;
        font-family: "Microsoft YaHei", "微软雅黑", Arial, sans-serif !important; /* 统一字体 */
        color: #000 !important; /* 黑色文字 */
    }

    /* 在同一行显示的备注信息样式 */
    .label-inline-info {
        font-size: 10px !important; /* 调大字体大小 */
        color: #000 !important;
        margin-left: 3px !important;
        white-space: nowrap !important;
        font-weight: bold !important; /* 使用粗体 */
        flex-shrink: 1 !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
        font-family: "Microsoft YaHei", "微软雅黑", Arial, sans-serif !important; /* 统一字体 */
    }

    .label-first-row {
        display: flex !important;
        justify-content: space-between !important;
        font-weight: bold !important; /* 使用粗体 */
        border-bottom: 1px solid #999 !important;
        padding-bottom: 1px !important;
        margin-bottom: 1px !important;
        font-family: "Microsoft YaHei", "微软雅黑", Arial, sans-serif !important; /* 统一字体 */
        color: #000 !important; /* 黑色文字 */
    }

    .label-name-price {
        flex: 1 !important;
        display: flex !important;
        justify-content: space-between !important;
        overflow: hidden !important;
        white-space: nowrap !important;
    }

    .label-remarks {
        font-size: 7px !important;
        color: #000 !important;
        margin-top: 0px !important;
        white-space: nowrap !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
        max-width: 100% !important;
        line-height: 0.8 !important;
        font-weight: bold !important;
    }

    /* 水洗唛瑕疵样式 */
    .label-flaw {
        font-size: 7px !important;
        color: #000 !important;
        margin-top: 0px !important;
        white-space: nowrap !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
        max-width: 100% !important;
        line-height: 0.8 !important;
        font-weight: bold !important;
    }

    /* 水洗唛衣物备注样式 */
    .label-item-remarks {
        font-size: 7px !important;
        color: #000 !important;
        margin-top: 0px !important;
        white-space: nowrap !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
        max-width: 100% !important;
        line-height: 0.8 !important;
        font-weight: bold !important;
    }

    /* 隐藏打印时不需要的元素 */
    .print-modal, .modal, .close-print-modal, .print-action-btn,
    .print-modal-header, .print-modal-footer, .label-selection-area,
    .edit-item-btn {
        display: none !important;
        visibility: hidden !important;
    }



    /* 设置页面边距 - 针对80mm热敏打印纸优化 */
    @page {
        margin: 2mm;
        size: 80mm auto;
    }



    /* 额外的打印优化 */
    body {
        -webkit-print-color-adjust: exact !important;
        color-adjust: exact !important;
        print-color-adjust: exact !important;
    }

    /* 小票表格打印优化 */
    .receipt-items tr {
        page-break-inside: avoid !important;
        page-break-after: auto !important;
    }
}
