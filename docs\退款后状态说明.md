# 退款后订单状态和支付状态说明

## 📋 退款后状态变化总览

### 🔄 订单状态 (order.status)

| 退款类型 | 退款前状态 | 退款后状态 | 说明 |
|---------|-----------|-----------|------|
| **全额退款** | 任何状态 | `已取消` | 全额退款后订单状态变为"已取消" |
| **部分退款** | 任何状态 | 保持不变 | 部分退款不改变订单状态，订单仍可继续处理 |

> **重要说明**: `已退赔` 状态专门用于记录有赔付的订单，普通退款使用 `已取消` 状态。

### 💰 支付状态 (order.payment_status)

| 退款类型 | 退款前状态 | 退款后状态 | 说明 |
|---------|-----------|-----------|------|
| **全额退款** | `已付款` | `已退款` | 表示订单已全额退款 |
| **全额退款** | `未付款` | `已退款` | 未付款订单也可以退款（如预付费等情况） |
| **部分退款** | `已付款` | `部分退款` | 表示订单已部分退款 |
| **部分退款** | `未付款` | `部分退款` | 未付款订单的部分退款 |

## 🎯 具体场景示例

### 场景1：全额退款
```
退款前：
- 订单状态：已配送
- 支付状态：已付款
- 订单金额：¥100.00

退款后：
- 订单状态：已取消 ✅
- 支付状态：已退款 ✅
- 退款金额：¥100.00
```

### 场景2：部分退款
```
退款前：
- 订单状态：已配送
- 支付状态：已付款
- 订单金额：¥100.00

退款后：
- 订单状态：已配送 (保持不变)
- 支付状态：部分退款 ✅
- 退款金额：¥50.00
```

### 场景3：未付款订单的全额退款
```
退款前：
- 订单状态：已上架
- 支付状态：未付款
- 订单金额：¥100.00

退款后：
- 订单状态：已取消 ✅
- 支付状态：已退款 ✅
- 退款金额：¥100.00
```

## 🔍 支付状态详细说明

### 支持的支付状态值

1. **`未付款`** - 订单尚未付款
2. **`已付款`** - 订单已完成付款
3. **`已退款`** - 订单已全额退款（新增）
4. **`部分退款`** - 订单已部分退款（新增）

### 支付状态的显示样式

- **未付款**: 红色背景 (#dc3545)
- **已付款**: 绿色背景 (#28a745)
- **已退款**: 灰色背景 (#6c757d)
- **部分退款**: 橙色背景 (#fd7e14)

## 🛠️ 技术实现

### 数据库字段
```sql
-- Order表中的相关字段
status VARCHAR(50) NOT NULL DEFAULT '门店已分拣'
payment_status VARCHAR(50) NOT NULL DEFAULT '未付款'
```

### 退款逻辑代码
```python
# 更新订单状态和支付状态
if refund_type == '全额退款':
    order.status = '已取消'  # 全额退款后订单状态改为已取消
    order.payment_status = '已退款'
else:
    # 部分退款时，支付状态改为部分退款
    order.payment_status = '部分退款'
```

## 📊 状态流转图

```
订单创建
    ↓
[门店已分拣] + [未付款/已付款]
    ↓
... (正常流程) ...
    ↓
[已配送] + [已付款]
    ↓
退款操作
    ↓
┌─────────────────┬─────────────────┐
│   全额退款      │   部分退款      │
├─────────────────┼─────────────────┤
│ [已取消]        │ [保持原状态]    │
│ [已退款]        │ [部分退款]      │
└─────────────────┴─────────────────┘

注：已退赔状态专门用于有赔付的订单
```

## ⚠️ 重要注意事项

### 1. 状态不可逆性
- 支付状态为 `已退款` 的订单表示已全额退款，不能再进行退款操作
- 全额退款后订单状态变为 `已取消`，表示因退款而取消的订单

### 2. 部分退款的特殊性
- 部分退款不改变订单状态，订单可以继续正常流程
- 支付状态变为 `部分退款` 后，仍可以进行后续的部分退款操作

### 3. 业务逻辑
- 全额退款后，订单状态变为 `已取消`，支付状态为 `已退款`
- 部分退款后，订单可以继续处理，但需要注意已退款的金额
- `已退赔` 状态专门用于有赔付的订单，与普通退款区分

### 4. 权限控制
- 只有管理员或订单的原操作员可以进行退款
- 退款操作会记录操作员信息和操作时间

## 🔄 状态查询和筛选

### 在历史订单页面
- 可以按支付状态筛选：未付款、已付款、已退款、部分退款
- 订单详情中会显示当前的订单状态和支付状态
- 支付状态用不同颜色标识，便于识别

### 批量操作
- 支持批量更新支付状态
- 包含新增的退款相关状态选项

## 📈 数据统计影响

### 收入统计
- 已退款订单不计入收入统计
- 部分退款订单按实际收款金额计入统计

### 报表生成
- 退款记录单独统计
- 可以生成退款明细报表
- 支持按时间段查询退款情况

---

**总结**: 退款功能完善后，系统能够准确跟踪和管理订单的退款状态。全额退款的订单状态变为"已取消"，与专门用于赔付的"已退赔"状态区分开来，为业务运营提供更精确的数据支持。
