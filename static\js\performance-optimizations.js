/**
 * 前端性能优化工具库
 * 包含缓存、防抖、虚拟滚动等优化功能
 */

// =====================================================
// 1. 缓存管理器
// =====================================================
class CacheManager {
    constructor(defaultTTL = 30000) { // 默认30秒缓存
        this.cache = new Map();
        this.defaultTTL = defaultTTL;
    }

    set(key, data, ttl = this.defaultTTL) {
        this.cache.set(key, {
            data: data,
            timestamp: Date.now(),
            ttl: ttl
        });
    }

    get(key) {
        const item = this.cache.get(key);
        if (!item) return null;

        if (Date.now() - item.timestamp > item.ttl) {
            this.cache.delete(key);
            return null;
        }

        return item.data;
    }

    clear() {
        this.cache.clear();
    }

    size() {
        return this.cache.size;
    }
}

// 全局缓存实例
const globalCache = new CacheManager();

// =====================================================
// 2. 防抖工具
// =====================================================
function debounce(func, wait, immediate = false) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            timeout = null;
            if (!immediate) func(...args);
        };
        const callNow = immediate && !timeout;
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
        if (callNow) func(...args);
    };
}

// =====================================================
// 3. 节流工具
// =====================================================
function throttle(func, limit) {
    let inThrottle;
    return function(...args) {
        if (!inThrottle) {
            func.apply(this, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

// =====================================================
// 4. 优化后的商场客户管理
// =====================================================
class OptimizedMallCustomerManager {
    constructor() {
        this.cache = new CacheManager(60000); // 1分钟缓存
        this.currentPage = 1;
        this.perPage = 10;
        this.isLoading = false;
        this.searchTimeout = null;
        
        this.initializeEventListeners();
    }

    initializeEventListeners() {
        // 搜索防抖
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.addEventListener('input', debounce((e) => {
                this.searchCustomers(e.target.value);
            }, 300));
        }

        // 状态筛选
        const statusFilter = document.getElementById('statusFilter');
        if (statusFilter) {
            statusFilter.addEventListener('change', (e) => {
                this.filterByStatus(e.target.value);
            });
        }
    }

    async loadCustomers(page = 1, searchTerm = '', statusFilter = '', useCache = true) {
        if (this.isLoading) return;

        const cacheKey = `customers-${page}-${searchTerm}-${statusFilter}`;
        
        // 检查缓存
        if (useCache) {
            const cachedData = this.cache.get(cacheKey);
            if (cachedData) {
                console.log('[PERF] 使用缓存数据');
                this.renderCustomers(cachedData);
                return;
            }
        }

        this.isLoading = true;
        this.showLoadingIndicator();

        try {
            const params = new URLSearchParams({
                page: page,
                per_page: this.perPage
            });

            if (searchTerm) params.append('search', searchTerm);
            if (statusFilter) params.append('status', statusFilter);

            console.log('[PERF] 发起API请求:', `/api/mall_customers?${params}`);
            const startTime = performance.now();

            const response = await fetch(`/api/mall_customers?${params}`);
            if (!response.ok) {
                throw new Error('网络响应失败');
            }

            const data = await response.json();
            const endTime = performance.now();
            
            console.log(`[PERF] API响应时间: ${(endTime - startTime).toFixed(2)}ms`);
            console.log(`[PERF] 服务器执行时间: ${(data.execution_time * 1000).toFixed(2)}ms`);

            // 缓存结果
            this.cache.set(cacheKey, data);
            
            this.renderCustomers(data);
            this.currentPage = page;

        } catch (error) {
            console.error('[ERROR] 加载客户列表失败:', error);
            this.showErrorMessage('加载客户列表失败: ' + error.message);
        } finally {
            this.isLoading = false;
            this.hideLoadingIndicator();
        }
    }

    searchCustomers(searchTerm) {
        console.log('[PERF] 搜索客户:', searchTerm);
        this.loadCustomers(1, searchTerm, document.getElementById('statusFilter')?.value || '');
    }

    filterByStatus(status) {
        console.log('[PERF] 状态筛选:', status);
        this.loadCustomers(1, document.getElementById('searchInput')?.value || '', status);
    }

    renderCustomers(data) {
        const tbody = document.getElementById('customerTableBody');
        if (!tbody) return;

        if (!data.customers || data.customers.length === 0) {
            tbody.innerHTML = '<tr><td colspan="9" class="text-center">暂无数据</td></tr>';
            return;
        }

        const html = data.customers.map(customer => `
            <tr>
                <td>${this.escapeHtml(customer.mall_name)}</td>
                <td>${this.escapeHtml(customer.contact_name || '-')}</td>
                <td>${this.escapeHtml(customer.contact_phone || '-')}</td>
                <td>${this.escapeHtml(customer.billing_cycle)}</td>
                <td>${customer.contract_end_date || '无限制'}</td>
                <td>
                    <span class="status-badge status-${customer.status.toLowerCase()}">
                        ${this.escapeHtml(customer.status)}
                    </span>
                </td>
                <td>
                    <span class="payment-status ${customer.payment_status === '正常' ? 'normal' : 'overdue'}">
                        ${this.escapeHtml(customer.payment_status)}
                    </span>
                </td>
                <td>${customer.discount_count}</td>
                <td>
                    <button class="btn btn-sm btn-info" onclick="showCustomerDetail(${customer.id})">
                        详情
                    </button>
                    <button class="btn btn-sm btn-warning" onclick="editCustomer(${customer.id})">
                        编辑
                    </button>
                </td>
            </tr>
        `).join('');

        tbody.innerHTML = html;
        this.renderPagination(data);
    }

    renderPagination(data) {
        // 分页渲染逻辑
        const paginationContainer = document.getElementById('pagination');
        if (!paginationContainer) return;

        const totalPages = data.pages;
        const currentPage = data.current_page;

        let paginationHtml = '';
        
        // 上一页
        if (currentPage > 1) {
            paginationHtml += `<button class="btn btn-sm btn-outline-primary" onclick="mallCustomerManager.goToPage(${currentPage - 1})">上一页</button>`;
        }

        // 页码
        const startPage = Math.max(1, currentPage - 2);
        const endPage = Math.min(totalPages, currentPage + 2);

        for (let i = startPage; i <= endPage; i++) {
            const activeClass = i === currentPage ? 'btn-primary' : 'btn-outline-primary';
            paginationHtml += `<button class="btn btn-sm ${activeClass}" onclick="mallCustomerManager.goToPage(${i})">${i}</button>`;
        }

        // 下一页
        if (currentPage < totalPages) {
            paginationHtml += `<button class="btn btn-sm btn-outline-primary" onclick="mallCustomerManager.goToPage(${currentPage + 1})">下一页</button>`;
        }

        paginationContainer.innerHTML = paginationHtml;
    }

    goToPage(page) {
        const searchTerm = document.getElementById('searchInput')?.value || '';
        const statusFilter = document.getElementById('statusFilter')?.value || '';
        this.loadCustomers(page, searchTerm, statusFilter);
    }

    showLoadingIndicator() {
        const tbody = document.getElementById('customerTableBody');
        if (tbody) {
            tbody.innerHTML = '<tr><td colspan="9" class="text-center">加载中...</td></tr>';
        }
    }

    hideLoadingIndicator() {
        // 加载指示器会在renderCustomers中被替换
    }

    showErrorMessage(message) {
        const tbody = document.getElementById('customerTableBody');
        if (tbody) {
            tbody.innerHTML = `<tr><td colspan="9" class="text-center text-danger">${this.escapeHtml(message)}</td></tr>`;
        }
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    // 清除缓存
    clearCache() {
        this.cache.clear();
        console.log('[PERF] 缓存已清除');
    }

    // 预加载下一页
    preloadNextPage() {
        const nextPage = this.currentPage + 1;
        const searchTerm = document.getElementById('searchInput')?.value || '';
        const statusFilter = document.getElementById('statusFilter')?.value || '';
        
        // 异步预加载，不影响当前页面
        setTimeout(() => {
            this.loadCustomers(nextPage, searchTerm, statusFilter, false);
        }, 1000);
    }
}

// =====================================================
// 5. 优化后的会员管理
// =====================================================
class OptimizedMemberManager {
    constructor() {
        this.cache = new CacheManager(45000); // 45秒缓存
        this.currentPage = 1;
        this.perPage = 10;
        this.isLoading = false;
        
        this.initializeEventListeners();
    }

    initializeEventListeners() {
        // 搜索防抖
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.addEventListener('input', debounce((e) => {
                this.searchMembers(e.target.value);
            }, 300));
        }

        // 状态筛选
        const statusFilter = document.getElementById('statusFilter');
        if (statusFilter) {
            statusFilter.addEventListener('change', (e) => {
                this.filterByStatus(e.target.value);
            });
        }
    }

    async loadMembers(page = 1, searchTerm = '', statusFilter = 'all', useCache = true) {
        if (this.isLoading) return;

        const cacheKey = `members-${page}-${searchTerm}-${statusFilter}`;
        
        // 检查缓存
        if (useCache) {
            const cachedData = this.cache.get(cacheKey);
            if (cachedData) {
                console.log('[PERF] 使用缓存数据');
                this.renderMembers(cachedData);
                return;
            }
        }

        this.isLoading = true;
        this.showLoadingIndicator();

        try {
            const params = new URLSearchParams({
                page: page,
                per_page: this.perPage
            });

            if (searchTerm) params.append('search', searchTerm);
            if (statusFilter !== 'all') params.append('status', statusFilter);

            console.log('[PERF] 发起会员API请求:', `/api/members?${params}`);
            const startTime = performance.now();

            const response = await fetch(`/api/members?${params}`);
            if (!response.ok) {
                throw new Error('网络响应失败');
            }

            const data = await response.json();
            const endTime = performance.now();
            
            console.log(`[PERF] 会员API响应时间: ${(endTime - startTime).toFixed(2)}ms`);
            console.log(`[PERF] 会员服务器执行时间: ${(data.execution_time * 1000).toFixed(2)}ms`);

            // 缓存结果
            this.cache.set(cacheKey, data);
            
            this.renderMembers(data);
            this.currentPage = page;

        } catch (error) {
            console.error('[ERROR] 加载会员列表失败:', error);
            this.showErrorMessage('加载会员列表失败: ' + error.message);
        } finally {
            this.isLoading = false;
            this.hideLoadingIndicator();
        }
    }

    searchMembers(searchTerm) {
        console.log('[PERF] 搜索会员:', searchTerm);
        this.loadMembers(1, searchTerm, document.getElementById('statusFilter')?.value || 'all');
    }

    filterByStatus(status) {
        console.log('[PERF] 会员状态筛选:', status);
        this.loadMembers(1, document.getElementById('searchInput')?.value || '', status);
    }

    renderMembers(data) {
        const tbody = document.getElementById('memberTableBody');
        if (!tbody) return;

        if (!data.members || data.members.length === 0) {
            tbody.innerHTML = '<tr><td colspan="8" class="text-center">暂无数据</td></tr>';
            return;
        }

        const html = data.members.map(member => `
            <tr>
                <td>${this.escapeHtml(member.name)}</td>
                <td>${this.escapeHtml(member.phone)}</td>
                <td>¥${member.total_balance.toFixed(2)}</td>
                <td>${(member.discount_rate * 100).toFixed(0)}%</td>
                <td>
                    <span class="activity-badge ${member.is_active ? 'active' : 'inactive'}">
                        ${member.is_active ? '活跃' : '不活跃'}
                    </span>
                </td>
                <td>${member.created_at.split('T')[0]}</td>
                <td>${member.updated_at.split('T')[0]}</td>
                <td>
                    <button class="btn btn-sm btn-info" onclick="showMemberDetail(${member.id})">
                        详情
                    </button>
                    <button class="btn btn-sm btn-warning" onclick="editMember(${member.id})">
                        编辑
                    </button>
                    <button class="btn btn-sm btn-success" onclick="showRechargeModal(${member.id})">
                        充值
                    </button>
                </td>
            </tr>
        `).join('');

        tbody.innerHTML = html;
        this.renderPagination(data);
    }

    renderPagination(data) {
        // 与商场客户管理类似的分页逻辑
        const paginationContainer = document.getElementById('memberPagination');
        if (!paginationContainer) return;

        const totalPages = data.total_pages;
        const currentPage = data.current_page;

        let paginationHtml = '';
        
        if (currentPage > 1) {
            paginationHtml += `<button class="btn btn-sm btn-outline-primary" onclick="memberManager.goToPage(${currentPage - 1})">上一页</button>`;
        }

        const startPage = Math.max(1, currentPage - 2);
        const endPage = Math.min(totalPages, currentPage + 2);

        for (let i = startPage; i <= endPage; i++) {
            const activeClass = i === currentPage ? 'btn-primary' : 'btn-outline-primary';
            paginationHtml += `<button class="btn btn-sm ${activeClass}" onclick="memberManager.goToPage(${i})">${i}</button>`;
        }

        if (currentPage < totalPages) {
            paginationHtml += `<button class="btn btn-sm btn-outline-primary" onclick="memberManager.goToPage(${currentPage + 1})">下一页</button>`;
        }

        paginationContainer.innerHTML = paginationHtml;
    }

    goToPage(page) {
        const searchTerm = document.getElementById('searchInput')?.value || '';
        const statusFilter = document.getElementById('statusFilter')?.value || 'all';
        this.loadMembers(page, searchTerm, statusFilter);
    }

    showLoadingIndicator() {
        const tbody = document.getElementById('memberTableBody');
        if (tbody) {
            tbody.innerHTML = '<tr><td colspan="8" class="text-center">加载中...</td></tr>';
        }
    }

    hideLoadingIndicator() {
        // 加载指示器会在renderMembers中被替换
    }

    showErrorMessage(message) {
        const tbody = document.getElementById('memberTableBody');
        if (tbody) {
            tbody.innerHTML = `<tr><td colspan="8" class="text-center text-danger">${this.escapeHtml(message)}</td></tr>`;
        }
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    clearCache() {
        this.cache.clear();
        console.log('[PERF] 会员缓存已清除');
    }
}

// =====================================================
// 6. 全局初始化
// =====================================================
let mallCustomerManager;
let memberManager;

document.addEventListener('DOMContentLoaded', function() {
    // 根据页面类型初始化对应的管理器
    if (document.getElementById('customerTableBody')) {
        mallCustomerManager = new OptimizedMallCustomerManager();
        mallCustomerManager.loadCustomers();
        console.log('[PERF] 商场客户管理器已初始化');
    }

    if (document.getElementById('memberTableBody')) {
        memberManager = new OptimizedMemberManager();
        memberManager.loadMembers();
        console.log('[PERF] 会员管理器已初始化');
    }
});

// =====================================================
// 7. 性能监控工具
// =====================================================
class PerformanceMonitor {
    static logApiCall(url, startTime, endTime, dataSize = 0) {
        const duration = endTime - startTime;
        console.log(`[PERF] API调用: ${url}`);
        console.log(`[PERF] 耗时: ${duration.toFixed(2)}ms`);
        if (dataSize > 0) {
            console.log(`[PERF] 数据大小: ${dataSize} bytes`);
            console.log(`[PERF] 传输速率: ${(dataSize / duration * 1000 / 1024).toFixed(2)} KB/s`);
        }
    }

    static logCacheHit(key) {
        console.log(`[PERF] 缓存命中: ${key}`);
    }

    static logCacheMiss(key) {
        console.log(`[PERF] 缓存未命中: ${key}`);
    }
}

// 导出给全局使用
window.PerformanceMonitor = PerformanceMonitor;
window.globalCache = globalCache;
window.debounce = debounce;
window.throttle = throttle;
