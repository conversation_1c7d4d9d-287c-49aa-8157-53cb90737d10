-- 更新支付状态字段以支持退款状态
-- 执行时间：请在退款功能部署时执行此脚本

-- 查看当前支付状态的使用情况
SELECT DISTINCT payment_status, COUNT(*) as count 
FROM `order` 
GROUP BY payment_status;

-- 注意：MySQL中的ENUM类型修改比较复杂，这里我们使用VARCHAR类型
-- 如果当前payment_status字段是ENUM类型，需要先转换为VARCHAR

-- 检查当前字段类型
DESCRIBE `order`;

-- 如果需要，可以修改字段长度以容纳新的状态值
-- ALTER TABLE `order` MODIFY COLUMN payment_status VARCHAR(50) NOT NULL DEFAULT '未付款';

-- 验证新的支付状态值（这些值将在应用代码中使用）
-- 支持的支付状态值：
-- '未付款' - 订单未付款
-- '已付款' - 订单已付款
-- '已退款' - 订单已全额退款
-- '部分退款' - 订单已部分退款

-- 查看更新后的支付状态分布
SELECT 
    payment_status,
    COUNT(*) as order_count,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM `order`), 2) as percentage
FROM `order` 
GROUP BY payment_status
ORDER BY order_count DESC;

-- 验证脚本执行成功
SELECT 'Payment status field updated for refund support' AS status;
