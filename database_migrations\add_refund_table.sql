-- 添加退款记录表
-- 执行时间：请在应用更新前执行此脚本

-- 创建退款记录表
CREATE TABLE IF NOT EXISTS refund_record (
    id INT AUTO_INCREMENT PRIMARY KEY,
    order_id INT NOT NULL,
    order_number VARCHAR(50) NOT NULL,
    customer_id INT NOT NULL,
    refund_amount DECIMAL(10,2) NOT NULL,
    original_amount DECIMAL(10,2) NOT NULL,
    original_payment_method VARCHAR(50) NOT NULL,
    refund_method VARCHAR(50) NOT NULL,
    refund_reason TEXT,
    refund_type VARCHAR(20) NOT NULL DEFAULT '全额退款',
    status VARCHAR(20) NOT NULL DEFAULT '已退款',
    operator VARCHAR(50) NOT NULL,
    approved_by VARCHAR(50),
    remarks TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    processed_at DATETIME,
    
    -- 外键约束
    FOREIGN KEY (order_id) REFERENCES `order`(id) ON DELETE CASCADE,
    FOREIG<PERSON> KEY (customer_id) REFERENCES customer(id) ON DELETE CASCADE,
    
    -- 索引
    INDEX idx_order_id (order_id),
    INDEX idx_customer_id (customer_id),
    INDEX idx_order_number (order_number),
    INDEX idx_created_at (created_at),
    INDEX idx_operator (operator)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='退款记录表';

-- 验证表是否创建成功
SELECT 'refund_record table created successfully' AS status;

-- 查看表结构
DESCRIBE refund_record;
