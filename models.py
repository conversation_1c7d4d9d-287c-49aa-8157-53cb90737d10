from flask_sqlalchemy import SQLAlchemy
from sqlalchemy.sql import func
import datetime
import json

db = SQLAlchemy()

class Customer(db.Model):
    __tablename__ = 'customer'  # 明确指定表名

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    phone = db.Column(db.String(20), nullable=False, unique=True)
    balance = db.Column(db.Float, default=0.0)  # 客户余额
    gift_balance = db.Column(db.Float, default=0.0)  # 赠送余额
    discount_rate = db.Column(db.Float, default=1.0)  # 会员专属折扣率
    discount_expiry = db.Column(db.Date, nullable=True)  # 折扣有效期
    orders = db.relationship('Order', backref='customer', lazy=True, foreign_keys='Order.customer_id')
    recharge_records = db.relationship('RechargeRecord', backref='customer', lazy=True, foreign_keys='RechargeRecord.customer_id')
    service_discounts = db.relationship('MemberServiceDiscount', backref='customer', lazy=True)
    created_at = db.Column(db.DateTime, default=datetime.datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.datetime.now, onupdate=datetime.datetime.now)
    is_mall_customer = db.Column(db.Boolean, default=False)  # 是否为商场客户
    mall_customer_id = db.Column(db.Integer, db.ForeignKey('mall_customer.id'), nullable=True)  # 关联的商场客户ID

    @property
    def total_balance(self):
        """总余额（充值余额 + 赠送余额）"""
        return (self.balance or 0.0) + (self.gift_balance or 0.0)

class RechargeRecord(db.Model):
    __tablename__ = 'recharge_record'

    id = db.Column(db.Integer, primary_key=True)
    customer_id = db.Column(db.Integer, db.ForeignKey('customer.id'), nullable=False)
    amount = db.Column(db.Float, nullable=False)
    gift_amount = db.Column(db.Float, default=0.0)  # 赠送金额
    payment_method = db.Column(db.String(50), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.datetime.now)
    operator = db.Column(db.String(100))  # 操作员
    remarks = db.Column(db.String(255))  # 备注

class Order(db.Model):
    """订单表"""
    __tablename__ = 'order'

    id = db.Column(db.Integer, primary_key=True)
    order_number = db.Column(db.String(50), unique=True, nullable=False)  # 订单编号
    customer_id = db.Column(db.Integer, db.ForeignKey('customer.id'), nullable=False)
    total_amount = db.Column(db.Float, default=0, nullable=False)
    payment_method = db.Column(db.String(50), nullable=False)
    payment_status = db.Column(db.String(50), nullable=False, default='未付款')  # 未付款, 已付款
    payment_time = db.Column(db.DateTime)  # 支付时间
    status = db.Column(db.String(50), nullable=False, default='门店已分拣')  # 新的订单状态: '门店已分拣', '送至工厂中', '送至分店中', '已送至分店', '已上架', '配送中', '已取消', '已退赔', '已自取', '已配送'
    address = db.Column(db.String(255))  # 客户地址
    operator = db.Column(db.String(50))  # 操作员/营业员
    created_at = db.Column(db.DateTime, default=datetime.datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.datetime.now, onupdate=datetime.datetime.now)
    delivery_time = db.Column(db.DateTime)  # 交付时间
    clothes = db.relationship('Clothing', backref='order', lazy=True)

    # 商场客户订单相关字段
    is_mall_order = db.Column(db.Boolean, default=False)  # 是否为商场客户订单
    mall_customer_id = db.Column(db.Integer, db.ForeignKey('mall_customer.id'), nullable=True)  # 关联的商场客户ID
    discount_amount = db.Column(db.Float, default=0.0)  # 订单折扣金额

    # 订单修改标记字段
    is_modified = db.Column(db.Boolean, default=False)  # 是否被修改过
    last_modified_at = db.Column(db.DateTime)  # 最后修改时间
    last_modified_by = db.Column(db.String(50))  # 最后修改人员
    modification_count = db.Column(db.Integer, default=0)  # 修改次数

    def __repr__(self):
        return f'<Order {self.order_number}>'

class Clothing(db.Model):
    __tablename__ = 'clothing'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    color = db.Column(db.String(50))
    services = db.Column(db.String(200), nullable=False)  # 存储JSON格式的服务类型列表
    special_requirements = db.Column(db.String(500))  # 存储JSON格式的特殊要求
    price = db.Column(db.Float, default=0)  # 单件衣物价格
    quantity = db.Column(db.Integer, default=1, nullable=False)  # 数量，默认为1件
    flaw = db.Column(db.String(200))  # 瑕疵描述
    remarks = db.Column(db.String(500))  # 备注
    customer_id = db.Column(db.Integer, db.ForeignKey('customer.id'), nullable=False)
    order_id = db.Column(db.Integer, db.ForeignKey('order.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.datetime.now)
    status = db.Column(db.String(20), default="待处理")  # 待处理, 处理中, 已完成

    # 商场客户折扣相关字段
    is_mall_order = db.Column(db.Boolean, default=False)  # 是否为商场客户订单衣物
    original_price = db.Column(db.Float)  # 原价（折扣前）
    discount_rate = db.Column(db.Float)  # 折扣率

class ClothingPhoto(db.Model):
    __tablename__ = 'clothing_photo'

    id = db.Column(db.Integer, primary_key=True)
    clothing_id = db.Column(db.Integer, db.ForeignKey('clothing.id'), nullable=False)
    image_path = db.Column(db.String(255), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.datetime.now)
    clothing = db.relationship('Clothing', backref=db.backref('photos', lazy=True))

class Staff(db.Model):
    """店员/营业员表"""
    __tablename__ = 'staff'

    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(50), unique=True, nullable=False)
    password = db.Column(db.String(255), nullable=False)  # 增加密码字段长度
    name = db.Column(db.String(50), nullable=False)  # 店员姓名
    phone = db.Column(db.String(20))  # 联系电话
    role = db.Column(db.String(20), default='staff')  # 角色: staff, manager
    area = db.Column(db.String(50))  # 区域
    is_active = db.Column(db.Boolean, default=True)  # 是否启用
    created_at = db.Column(db.DateTime, default=datetime.datetime.now)
    last_login = db.Column(db.DateTime)

    def __repr__(self):
        return f'<Staff {self.username}>'

class Admin(db.Model):
    """管理员表"""
    __tablename__ = 'admin'

    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(50), unique=True, nullable=False)
    password = db.Column(db.String(255), nullable=False)  # 增加密码字段长度
    created_at = db.Column(db.DateTime, default=datetime.datetime.now)
    last_login = db.Column(db.DateTime)

    def __repr__(self):
        return f'<Admin {self.username}>'

class MallCustomer(db.Model):
    """商场客户模型"""
    __tablename__ = 'mall_customer'

    id = db.Column(db.Integer, primary_key=True)
    mall_name = db.Column(db.String(100), nullable=False)
    address = db.Column(db.String(255))
    phone = db.Column(db.String(20))
    contract_start_date = db.Column(db.Date)
    contract_end_date = db.Column(db.Date)
    billing_cycle = db.Column(db.String(50), default='月结')  # 月结、季度结算等
    contact_name = db.Column(db.String(50))
    contact_phone = db.Column(db.String(20))
    contact_position = db.Column(db.String(50))
    status = db.Column(db.String(20), default='活跃')  # 活跃、暂停、已终止
    remarks = db.Column(db.String(500))
    overall_discount_rate = db.Column(db.Float, default=1.0)  # 整体折扣率，1.0表示不打折
    area = db.Column(db.String(50))  # 所属区域，与Staff表的area字段保持一致
    created_at = db.Column(db.DateTime, default=datetime.datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.datetime.now, onupdate=datetime.datetime.now)

    # 关联关系
    product_discounts = db.relationship('MallProductDiscount', backref='mall_customer', lazy=True)
    monthly_bills = db.relationship('MallMonthlyBill', backref='mall_customer', lazy=True)

    def __repr__(self):
        return f'<MallCustomer {self.mall_name}>'

class RechargeGiftRule(db.Model):
    """充值赠送规则表"""
    __tablename__ = 'recharge_gift_rules'

    id = db.Column(db.Integer, primary_key=True)
    min_amount = db.Column(db.Float, nullable=False)  # 最小充值金额
    gift_type = db.Column(db.String(20), nullable=False)  # 赠送类型：percentage/fixed
    gift_value = db.Column(db.Float, nullable=False)  # 赠送值
    is_active = db.Column(db.Boolean, default=True)  # 是否启用
    created_at = db.Column(db.DateTime, default=datetime.datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.datetime.now, onupdate=datetime.datetime.now)

    def __repr__(self):
        return f'<RechargeGiftRule {self.min_amount}-{self.gift_type}-{self.gift_value}>'

class MemberServiceDiscount(db.Model):
    """会员服务折扣表"""
    __tablename__ = 'member_service_discounts'

    id = db.Column(db.Integer, primary_key=True)
    customer_id = db.Column(db.Integer, db.ForeignKey('customer.id'), nullable=False)
    service_type = db.Column(db.String(20), nullable=False)  # 服务类型：洗衣/织补/改衣/其他
    discount_rate = db.Column(db.Float, nullable=False)  # 折扣率
    valid_from = db.Column(db.Date, nullable=False)  # 生效日期
    valid_to = db.Column(db.Date, nullable=False)  # 失效日期
    is_active = db.Column(db.Boolean, default=True)  # 是否启用
    created_at = db.Column(db.DateTime, default=datetime.datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.datetime.now, onupdate=datetime.datetime.now)

    def __repr__(self):
        return f'<MemberServiceDiscount {self.customer_id}-{self.service_type}-{self.discount_rate}>'

class MallProductDiscount(db.Model):
    """商场产品折扣模型"""
    __tablename__ = 'mall_product_discount'

    id = db.Column(db.Integer, primary_key=True)
    mall_customer_id = db.Column(db.Integer, db.ForeignKey('mall_customer.id'), nullable=False)
    product_name = db.Column(db.String(100), nullable=False)
    product_type = db.Column(db.String(50), nullable=False)
    discount_rate = db.Column(db.Float, default=1.0, nullable=False)  # 1.0 表示不打折
    effective_date = db.Column(db.Date, nullable=False)
    expiry_date = db.Column(db.Date)  # 为空表示长期有效
    created_by = db.Column(db.String(50))
    created_at = db.Column(db.DateTime, default=datetime.datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.datetime.now, onupdate=datetime.datetime.now)

    def __repr__(self):
        return f'<MallProductDiscount {self.product_name} - {self.discount_rate}>'

class MallMonthlyBill(db.Model):
    """商场月度账单模型"""
    __tablename__ = 'mall_monthly_bill'

    id = db.Column(db.Integer, primary_key=True)
    mall_customer_id = db.Column(db.Integer, db.ForeignKey('mall_customer.id'), nullable=False)
    bill_year_month = db.Column(db.String(7), nullable=False)  # 格式: YYYY-MM
    bill_start_date = db.Column(db.Date, nullable=False)
    bill_end_date = db.Column(db.Date, nullable=False)
    order_count = db.Column(db.Integer, default=0)
    original_amount = db.Column(db.Float, default=0.0)  # 原始总金额（折扣前）
    total_amount = db.Column(db.Float, default=0.0)  # 实际总金额（折扣后）
    discount_amount = db.Column(db.Float, default=0.0)  # 折扣总金额
    actual_amount = db.Column(db.Float, default=0.0)  # 应收金额（等于total_amount）
    payment_status = db.Column(db.String(20), default='未付款')  # 未付款、部分付款、已付款
    payment_date = db.Column(db.Date)
    payment_method = db.Column(db.String(50))
    remarks = db.Column(db.String(500))
    created_at = db.Column(db.DateTime, default=datetime.datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.datetime.now, onupdate=datetime.datetime.now)

    # 添加与订单的关联关系
    orders = db.relationship('Order', primaryjoin="and_(Order.mall_customer_id==MallMonthlyBill.mall_customer_id, "
                           "func.substr(Order.created_at, 1, 7)==MallMonthlyBill.bill_year_month)",
                           viewonly=True, foreign_keys="[Order.mall_customer_id]")

    def __repr__(self):
        return f'<MallMonthlyBill {self.bill_year_month} - {self.mall_customer_id}>'

class MallDiscountHistory(db.Model):
    """折扣变更历史模型"""
    __tablename__ = 'mall_discount_history'

    id = db.Column(db.Integer, primary_key=True)
    mall_customer_id = db.Column(db.Integer, db.ForeignKey('mall_customer.id'), nullable=False)
    product_name = db.Column(db.String(100), nullable=False)
    product_type = db.Column(db.String(50), nullable=False)
    old_discount_rate = db.Column(db.Float)
    new_discount_rate = db.Column(db.Float, nullable=False)
    change_date = db.Column(db.Date, nullable=False)
    change_reason = db.Column(db.String(255))
    operator = db.Column(db.String(50))
    created_at = db.Column(db.DateTime, default=datetime.datetime.now)
    mall_customer = db.relationship('MallCustomer', backref='discount_histories')

    def __repr__(self):
        return f'<MallDiscountHistory {self.product_name}>'


class Product(db.Model):
    """商品/衣物管理模型"""
    __tablename__ = 'product'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False, unique=True)
    category = db.Column(db.String(50), nullable=False)  # 商品分类:上衣,裤装,外套,裙装,套装,其他
    # price字段已废弃，保留用于数据库兼容性，但不再使用
    price = db.Column(db.Float, default=0.0, nullable=True)  # 已废弃的基础价格字段
    description = db.Column(db.String(255))  # 商品描述
    is_active = db.Column(db.Boolean, default=True)  # 是否启用

    # 服务类型价格字段
    wash_price = db.Column(db.Float, default=15.0, nullable=False)  # 洗衣价格
    mend_price = db.Column(db.Float, default=20.0, nullable=False)  # 织补价格
    alter_price = db.Column(db.Float, default=30.0, nullable=False)  # 改衣价格
    other_price = db.Column(db.Float, default=20.0, nullable=False)  # 其他服务价格

    created_at = db.Column(db.DateTime, default=datetime.datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.datetime.now, onupdate=datetime.datetime.now)

    def __repr__(self):
        return f'<Product {self.name}>'

    def to_dict(self):
        """返回商品字典，不包含已废弃的基础价格字段"""
        return {
            'id': self.id,
            'name': self.name,
            'category': self.category,
            'description': self.description,
            'is_active': self.is_active,
            'wash_price': self.wash_price,
            'mend_price': self.mend_price,
            'alter_price': self.alter_price,
            'other_price': self.other_price
        }

class OrderStatusLog(db.Model):
    """订单状态变更日志表"""
    __tablename__ = 'order_status_log'

    id = db.Column(db.Integer, primary_key=True)
    order_id = db.Column(db.Integer, db.ForeignKey('order.id'), nullable=False)
    old_status = db.Column(db.String(50))
    new_status = db.Column(db.String(50), nullable=False)
    changed_by = db.Column(db.String(50))  # 操作人
    remarks = db.Column(db.String(255))    # 备注
    created_at = db.Column(db.DateTime, default=datetime.datetime.now)

    order = db.relationship('Order', backref='status_logs')

class RefundRecord(db.Model):
    """退款记录表"""
    __tablename__ = 'refund_record'

    id = db.Column(db.Integer, primary_key=True)
    order_id = db.Column(db.Integer, db.ForeignKey('order.id'), nullable=False)
    order_number = db.Column(db.String(50), nullable=False)  # 冗余字段，便于查询
    customer_id = db.Column(db.Integer, db.ForeignKey('customer.id'), nullable=False)
    refund_amount = db.Column(db.Float, nullable=False)  # 退款金额
    original_amount = db.Column(db.Float, nullable=False)  # 原订单金额
    original_payment_method = db.Column(db.String(50), nullable=False)  # 原支付方式
    refund_method = db.Column(db.String(50), nullable=False)  # 退款方式：余额退回、现金退款、原路退回等
    refund_reason = db.Column(db.String(500))  # 退款原因
    refund_type = db.Column(db.String(20), nullable=False, default='全额退款')  # 退款类型：全额退款、部分退款
    status = db.Column(db.String(20), nullable=False, default='已退款')  # 退款状态：已退款、退款中、退款失败
    operator = db.Column(db.String(50), nullable=False)  # 操作员
    approved_by = db.Column(db.String(50))  # 审批人（如果需要审批）
    remarks = db.Column(db.String(500))  # 备注
    created_at = db.Column(db.DateTime, default=datetime.datetime.now)
    processed_at = db.Column(db.DateTime)  # 处理时间

    # 关联关系
    order = db.relationship('Order', backref='refund_records')
    customer = db.relationship('Customer', backref='refund_records')

    def __repr__(self):
        return f'<RefundRecord {self.order_number} - {self.refund_amount}>'

def init_db(app):
    """初始化数据库"""
    db.init_app(app)
    with app.app_context():
        db.create_all()